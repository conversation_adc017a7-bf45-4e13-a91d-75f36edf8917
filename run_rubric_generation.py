#!/usr/bin/env python3
"""
Script to run rubric generation with various options
"""

from preprocess import PlatoPreprocess
from models import Part, db
from app import app
import sys

def show_usage():
    """Show usage information"""
    print("""
Usage: python run_rubric_generation.py [command] [options]

Commands:
    generate [--workers N] [--part-ids ID1 ID2 ...] [--force]
        Generate rubrics for parts
        --workers N: Number of parallel workers (default: 4)
        --part-ids: Specific part IDs to process (default: all parts without rubrics)
        --force: Process even parts that already have rubrics
    
    list
        List all parts with generated rubrics
    
    show PART_ID
        Show rubrics for a specific part
    
    stats
        Show statistics about rubric generation
    
    test
        Run a quick test on the first part

Examples:
    python run_rubric_generation.py generate --workers 8
    python run_rubric_generation.py generate --part-ids 1 2 3 --force
    python run_rubric_generation.py show 1
    python run_rubric_generation.py list
""")

def generate_rubrics(workers=4, part_ids=None, force=False):
    """Generate rubrics with specified options"""
    print(f"Generating rubrics with {workers} workers...")
    if part_ids:
        print(f"Processing specific parts: {part_ids}")
    if force:
        print("Force mode: will regenerate existing rubrics")
    
    plato = PlatoPreprocess(max_workers=workers)
    plato.work(part_ids=part_ids, skip_existing=not force)

def list_parts():
    """List all parts with rubrics"""
    plato = PlatoPreprocess()
    parts = plato.list_parts_with_rubrics()
    
    if not parts:
        print("No parts with rubrics found.")
        return
    
    print(f"Found {len(parts)} parts with rubrics:")
    print("-" * 80)
    for part in parts:
        print(f"ID: {part['id']:3d} | Score: {part['score']:2d} | Generated: {part['generated_at']}")
        print(f"     {part['description']}")
        print()

def show_part_rubrics(part_id):
    """Show rubrics for a specific part"""
    plato = PlatoPreprocess()
    rubrics = plato.get_rubrics_from_db(part_id)
    
    if not rubrics:
        print(f"No rubrics found for part {part_id}")
        return
    
    print(f"Rubrics for Part {part_id}")
    print("=" * 50)
    print(f"Generated at: {rubrics['generated_at']}")
    print()
    
    print("Content Requirements:")
    print("-" * 20)
    for req_id, requirement in rubrics['content_rubric'].items():
        print(f"  {req_id}: {requirement}")
    print()
    
    print("Scoring Rubric (bit-mapped):")
    print("-" * 30)
    for bit_combo, score in rubrics['scoring_rubric'].items():
        print(f"  Combination {bit_combo}: {score} points")

def show_stats():
    """Show statistics about rubric generation"""
    with app.app_context():
        total_parts = Part.query.count()
        parts_with_rubrics = Part.query.filter(
            Part.content_rubric.isnot(None),
            Part.scoring_rubric.isnot(None)
        ).count()
        
        print("Rubric Generation Statistics")
        print("=" * 30)
        print(f"Total parts: {total_parts}")
        print(f"Parts with rubrics: {parts_with_rubrics}")
        print(f"Parts without rubrics: {total_parts - parts_with_rubrics}")
        print(f"Completion rate: {(parts_with_rubrics/total_parts*100):.1f}%" if total_parts > 0 else "N/A")

def run_test():
    """Run a quick test"""
    print("Running quick test...")
    plato = PlatoPreprocess(max_workers=1)
    plato.work(part_ids=None, skip_existing=False)

def main():
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    if command == "generate":
        workers = 4
        part_ids = None
        force = False
        
        i = 2
        while i < len(sys.argv):
            if sys.argv[i] == "--workers" and i + 1 < len(sys.argv):
                workers = int(sys.argv[i + 1])
                i += 2
            elif sys.argv[i] == "--part-ids":
                part_ids = []
                i += 1
                while i < len(sys.argv) and not sys.argv[i].startswith("--"):
                    part_ids.append(int(sys.argv[i]))
                    i += 1
            elif sys.argv[i] == "--force":
                force = True
                i += 1
            else:
                i += 1
        
        generate_rubrics(workers, part_ids, force)
    
    elif command == "list":
        list_parts()
    
    elif command == "show":
        if len(sys.argv) < 3:
            print("Please specify a part ID")
            return
        part_id = int(sys.argv[2])
        show_part_rubrics(part_id)
    
    elif command == "stats":
        show_stats()
    
    elif command == "test":
        run_test()
    
    else:
        print(f"Unknown command: {command}")
        show_usage()

if __name__ == "__main__":
    main()
