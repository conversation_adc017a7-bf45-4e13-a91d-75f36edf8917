from dotenv import load_dotenv
import json
import os
import logging
import google.generativeai as genai
from pinecone import Pinecone
from models import Part
from app import app
import re

load_dotenv()

class PlatoPreprocess():
    def __init__(self):
        self.gemini_client = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
        self.pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
        self.pinecone_index = self.pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))
    
    def work(self):
        with app.app_context():
            part = Part.query.filter_by(id=1).first()
            content_rubrics = self.generate_content_requirements(part)
            scoring_rubrics = self.generate_scoring_rubrics(content_rubrics, part)
            print(content_rubrics, scoring_rubrics)

    def generate_content_requirements(self, part: Part) -> dict:
        """Generate concise content requirements for grading responses"""
        context_text = ""

        try:
            # Get context from Pinecone (reusing existing query logic)
            query_text = f"{part.description}"
            if part.answer:
                query_text += f" {part.answer}"
            if part.question.topic:
                query_text += f" {part.question.topic.name}"
            if part.question.topic.subject:
                query_text += f" {part.question.topic.subject.name}"

            query_payload = {
                "inputs": {
                    "text": query_text
                },
                "top_k": 5
            }

            query_response = self.pinecone_index.search(
                namespace="__default__",
                query=query_payload
            )

            for item in query_response['result']['hits']:
                context_text += f"{item['fields']['title']}: {item['fields']['content']}\n"

        except Exception as e_pinecone:
            logging.logger.warning(f"Pinecone query failed for part {part.id}: {str(e_pinecone)}")

        prompt = f"""
You are an expert educational assessment designer. Create concise content requirements for grading responses to this question.

CONTEXT INFORMATION:
{context_text}

QUESTION:
{part.description}

MODEL ANSWER:
{part.answer or "No model answer provided"}

TOTAL SCORE: {part.score} points

Based on the question, model answer, and context, identify the key content requirements that must be present in a correct response.
For each requirement:
1. Focus on specific concepts, facts, or explanations that must be included
2. Make each requirement clear, objective, and assessable
3. Ensure requirements are distinct from each other
4. Cover all essential elements of the model answer

Return ONLY a valid JSON dictionary where each key is a unique ID and each value is a specific content requirement.
Example: {{"1": "Correctly identifies ionic bonding", "2": "Explains effect of charge on bond strength"}}
"""

        try:
            response = self.gemini_client.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.2,
                    'response_mime_type': 'application/json'
                }
            )
            
            requirements = json.loads(response.text)
            return requirements
        except Exception as e:
            logging.logger.error(f"Failed to generate content requirements for part {part.id}: {str(e)}")
            return {}

    def generate_scoring_rubrics(self, content_rubrics: dict, part: Part) -> dict:
        """Generate scoring rubrics with bit-mapped requirement combinations"""
    
        # Format content requirements for the prompt
        content_requirements_text = ""
        for i, (req_id, requirement) in enumerate(content_rubrics.items()):
            content_requirements_text += f"- Bit {i} ({req_id}): {requirement}\n"
        
        prompt = f"""
You are an expert educational assessment designer. Create a bit-mapped scoring rubric for grading responses.

QUESTION:
{part.description}

MODEL ANSWER:
{part.answer or "No model answer provided"}

TOTAL SCORE: {part.score} points

CONTENT REQUIREMENTS (with bit positions):
{content_requirements_text}

Create a scoring rubric where each requirement is represented by a bit position:
- If requirement at bit 0 is present, toggle bit 0 (value 1)
- If requirement at bit 1 is present, toggle bit 1 (value 2)
- If requirement at bit 2 is present, toggle bit 2 (value 4)
- And so on (bit 3 = 8, bit 4 = 16, etc.)

For example, with 3 requirements (bits 0,1,2):
- All requirements present = bits 0,1,2 = 1+2+4 = 7
- Only requirements at bits 0,1 present = 1+2 = 3
- Only requirement at bit 2 present = 4

Return ONLY a valid JSON dictionary where:
- Each key is an integer representing the bit combination
- Each value is the score that combination earns

Example format for a 3-point question with 3 requirements:
{{
  "7": 3,  // All requirements present (bits 0,1,2)
  "3": 2,  // Requirements at bits 0,1 present, bit 2 missing
  "4": 1,  // Only requirement at bit 2 present
  "0": 0   // No requirements present
}}

Ensure you cover all meaningful combinations and assign appropriate scores from 0 to {part.score}.
If a requirement is important for the overall understanding of a question, all combinations omitting it should be assigned a score of 0.
"""

        try:
            response = self.gemini_client.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.2,
                    'response_mime_type': 'application/json'
                }
            )
            
            # Clean the response text to remove any comments
            response_text = response.text
            response_text = re.sub(r'//.*', '', response_text)  # Remove any comments
            
            scoring_rubrics = json.loads(response_text)
            
            # Convert string keys to integers
            scoring_rubrics = {int(k): v for k, v in scoring_rubrics.items()}
            
            return scoring_rubrics
        except Exception as e:
            logging.logger.error(f"Failed to generate bit-mapped scoring rubrics for part {part.id}: {str(e)}")
            return {}

plato = PlatoPreprocess()
plato.work()